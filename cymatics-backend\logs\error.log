{"clientVersion":"5.22.0","errorCode":"P1001","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase connection failed: Can't reach database server at `localhost:5433`\u001b[39m\n\n\u001b[31mPlease make sure your database server is running at `localhost:5433`.\u001b[39m","name":"PrismaClientInitializationError","stack":"PrismaClientInitializationError: Can't reach database server at `localhost:5433`\n\nPlease make sure your database server is running at `localhost:5433`.\n    at t (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:112:2488)\n    at async connectDatabase (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\config\\database.ts:28:5)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\server.ts:45:5)","timestamp":"2025-06-08 21:05:05:55"}
{"clientVersion":"5.22.0","errorCode":"P1000","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mDatabase connection failed: Authentication failed against database server at `localhost`, the provided database credentials for `postgres` are not valid.\u001b[39m\n\n\u001b[31mPlease make sure to provide valid database credentials for the database server at `localhost`.\u001b[39m","name":"PrismaClientInitializationError","stack":"PrismaClientInitializationError: Authentication failed against database server at `localhost`, the provided database credentials for `postgres` are not valid.\n\nPlease make sure to provide valid database credentials for the database server at `localhost`.\n    at t (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\@prisma\\client\\runtime\\library.js:112:2488)\n    at async connectDatabase (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\config\\database.ts:28:5)\n    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\src\\server.ts:45:5)","timestamp":"2025-06-08 21:07:47:747"}
{"code":"EAUTH","command":"AUTH PLAIN","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mEmail service connection failed: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\u001b[39m\n\u001b[31m535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-31349ffc151sm4149856a91.48 - gsmtp\u001b[39m","response":"535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-31349ffc151sm4149856a91.48 - gsmtp","responseCode":535,"stack":"Error: Invalid login: 535-5.7.8 Username and Password not accepted. For more information, go to\n535 5.7.8  https://support.google.com/mail/?p=BadCredentials 98e67ed59e1d1-31349ffc151sm4149856a91.48 - gsmtp\n    at SMTPConnection._formatError (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:809:19)\n    at SMTPConnection._actionAUTHComplete (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:1588:34)\n    at SMTPConnection.<anonymous> (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:556:26)\n    at SMTPConnection._processResponse (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:993:20)\n    at SMTPConnection._onData (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:774:14)\n    at TLSSocket.SMTPConnection._onSocketData (C:\\Users\\<USER>\\OneDrive\\Desktop\\Cymatics\\cymatics-backend\\node_modules\\nodemailer\\lib\\smtp-connection\\index.js:195:44)\n    at TLSSocket.emit (node:events:524:28)\n    at TLSSocket.emit (node:domain:489:12)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)","timestamp":"2025-06-08 21:08:20:820"}
